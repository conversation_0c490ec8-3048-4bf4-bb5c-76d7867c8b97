from sqlalchemy import create_engine, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import OperationalError, StatementError
from app.core.config import settings
import logging
import asyncio
import time
from functools import wraps
from typing import Callable, Any

logger = logging.getLogger(__name__)

# Create database engine
connect_args = {}
if "sqlite" in settings.DATABASE_URL:
    connect_args = {
        "check_same_thread": False,
        "timeout": 30  # 30 second timeout for SQLite operations
    }

engine = create_engine(
    settings.DATABASE_URL,
    echo=settings.DEBUG,
    pool_pre_ping=True,
    connect_args=connect_args
)

# Configure SQLite for better concurrency if using SQLite
if "sqlite" in settings.DATABASE_URL:
    @event.listens_for(engine, "connect")
    def set_sqlite_pragma(dbapi_connection, _connection_record):
        cursor = dbapi_connection.cursor()
        # Enable WAL mode for better concurrency
        cursor.execute("PRAGMA journal_mode=WAL")
        # Set synchronous mode for better performance
        cursor.execute("PRAGMA synchronous=NORMAL")
        # Increase cache size
        cursor.execute("PRAGMA cache_size=1000")
        # Use memory for temp storage
        cursor.execute("PRAGMA temp_store=memory")
        # Set busy timeout to handle locks
        cursor.execute("PRAGMA busy_timeout=30000")  # 30 seconds
        cursor.close()
        logger.info("SQLite WAL mode and optimizations enabled")

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()

def get_db():
    """Dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def db_retry(max_retries: int = 3, backoff_factor: float = 0.1):
    """Decorator to retry database operations on SQLite lock errors"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except OperationalError as e:
                    last_exception = e
                    error_msg = str(e).lower()
                    if "database is locked" in error_msg or "database table is locked" in error_msg:
                        if attempt < max_retries - 1:
                            sleep_time = backoff_factor * (2 ** attempt)
                            logger.warning(f"Database locked, retrying in {sleep_time}s (attempt {attempt + 1}/{max_retries})")
                            time.sleep(sleep_time)
                            continue
                    raise
                except StatementError as e:
                    last_exception = e
                    if "database is locked" in str(e).lower() and attempt < max_retries - 1:
                        sleep_time = backoff_factor * (2 ** attempt)
                        logger.warning(f"Database locked, retrying in {sleep_time}s (attempt {attempt + 1}/{max_retries})")
                        time.sleep(sleep_time)
                        continue
                    raise
            raise last_exception
        return wrapper
    return decorator

async def db_retry_async(max_retries: int = 3, backoff_factor: float = 0.1):
    """Async decorator to retry database operations on SQLite lock errors"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except OperationalError as e:
                    last_exception = e
                    error_msg = str(e).lower()
                    if "database is locked" in error_msg or "database table is locked" in error_msg:
                        if attempt < max_retries - 1:
                            sleep_time = backoff_factor * (2 ** attempt)
                            logger.warning(f"Database locked, retrying in {sleep_time}s (attempt {attempt + 1}/{max_retries})")
                            await asyncio.sleep(sleep_time)
                            continue
                    raise
                except StatementError as e:
                    last_exception = e
                    if "database is locked" in str(e).lower() and attempt < max_retries - 1:
                        sleep_time = backoff_factor * (2 ** attempt)
                        logger.warning(f"Database locked, retrying in {sleep_time}s (attempt {attempt + 1}/{max_retries})")
                        await asyncio.sleep(sleep_time)
                        continue
                    raise
            raise last_exception
        return wrapper
    return decorator

def safe_commit(db, rollback_on_error: bool = True):
    """Safely commit database changes with proper error handling"""
    try:
        db.commit()
        return True
    except OperationalError as e:
        error_msg = str(e).lower()
        if rollback_on_error:
            db.rollback()
        if "database is locked" in error_msg:
            logger.error(f"Database lock error during commit: {e}")
            raise OperationalError(f"Database is currently locked. Please try again.", None, None)
        else:
            logger.error(f"Database operational error during commit: {e}")
            raise
    except Exception as e:
        if rollback_on_error:
            db.rollback()
        logger.error(f"Unexpected error during commit: {e}")
        raise
